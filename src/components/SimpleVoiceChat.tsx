import React, { useEffect, useRef, useState } from "react";
import { useRealTimeConversation } from "../hooks/useRealTimeConversation";
import { GameProgress } from "./GameProgress";
import { HintsPopup } from "./HintsPopup";
import { AnimatedVoiceButton } from "./AnimatedVoiceButton";
import { ConversationStateIndicator } from "./ConversationStateIndicator";
import { ChatArea } from "./ChatArea";
import { CharacterInfo } from "./CharacterInfo";
import { MovistarInfo } from "./MovistarInfo";
import { ConversationStorage } from "../services/ConversationStorage";
import type { GameProgress as GameProgressType } from "../services/ConversationStorage";

interface SimpleVoiceChatProps {
  generatedCharacter?: string;
  isGameStarted: boolean;
  initialMessage?: string;
}

/**
 * Main voice chat component for the character guessing game
 *
 * Features:
 * - Real-time voice conversation with AI
 * - Game progress tracking
 * - Hints system integration
 * - Automatic conversation state management
 */

export const SimpleVoiceChat: React.FC<SimpleVoiceChatProps> = ({
  generatedCharacter,
  isGameStarted,
  initialMessage,
}) => {
  // Hook for real-time conversation management
  const {
    isActive,
    conversationState,
    messages,
    isSupported,
    error,
    startConversation,
    stopConversation,
    enableSmartMicrophone,
    addInitialMessage,
  } = useRealTimeConversation(generatedCharacter, isGameStarted);

  // Refs and state for game management
  const autoStartAttempted = useRef(false);
  const [gameProgress, setGameProgress] = useState<GameProgressType | null>(null);
  const [showHintsPopup, setShowHintsPopup] = useState(false);
  const [showCharacterInfo, setShowCharacterInfo] = useState(false);
  const [showMovistarInfo, setShowMovistarInfo] = useState(false);
  const conversationStorage = useRef(ConversationStorage.getInstance());

  /**
   * Add initial message when it arrives
   */
  useEffect(() => {
    if (initialMessage && isGameStarted) {
      addInitialMessage(initialMessage);
    }
  }, [initialMessage, isGameStarted, addInitialMessage]);

  /**
   * Update game progress when session changes
   */
  useEffect(() => {
    if (isGameStarted) {
      const updateProgress = () => {
        const progress = conversationStorage.current.getGameProgress();
        setGameProgress(progress);
      };

      updateProgress();
      const interval = setInterval(updateProgress, 1000);
      return () => clearInterval(interval);
    }
  }, [isGameStarted, messages]);

  /**
   * Debug logging for state changes
   */
  useEffect(() => {
    // console.log("🔄 isActive cambió a:", isActive);
  }, [isActive]);

  useEffect(() => {
    // console.log("🔄 conversationState cambió a:", conversationState);

    // Auto-start evaluation when state becomes idle
    if (
      conversationState === "idle" &&
      isGameStarted &&
      !isActive &&
      isSupported &&
      !error &&
      initialMessage &&
      !autoStartAttempted.current
    ) {
      // console.log("🎤 Estado cambió a idle, verificando auto-start...");
    }
  }, [conversationState, isGameStarted, isActive, isSupported, error, initialMessage]);

  /**
   * Reset auto-start flag when game restarts
   */
  useEffect(() => {
    if (!isGameStarted) {
      autoStartAttempted.current = false;
    }
  }, [isGameStarted]);

  /**
   * Auto-open Movistar info when character is generated and game starts
   */
  useEffect(() => {
    if (isGameStarted && generatedCharacter && generatedCharacter.trim()) {
      // Delay opening to allow the game to fully initialize
      const timer = setTimeout(() => {
        setShowMovistarInfo(true);
      }, 2000); // 2 seconds delay

      return () => clearTimeout(timer);
    }
  }, [isGameStarted, generatedCharacter]);

  /**
   * Handle voice button click - start/stop conversation
   */
  const handleButtonClick = () => {
    if (isActive) {
      stopConversation();
    } else {
      startConversation().then((success) => {
        if (success) {
          enableSmartMicrophone();
        }
      });
    }
  };

  /**
   * Handle hints popup display
   */
  const handleShowHints = () => setShowHintsPopup(true);
  const handleCloseHints = () => setShowHintsPopup(false);

  /**
   * Handle character info display
   */
  const handleShowCharacterInfo = () => setShowCharacterInfo(true);
  const handleCloseCharacterInfo = () => setShowCharacterInfo(false);

  /**
   * Handle Movistar info display
   */
  const handleShowMovistarInfo = () => setShowMovistarInfo(true);
  const handleCloseMovistarInfo = () => setShowMovistarInfo(false);

  if (!isGameStarted) {
    return null;
  }

  if (!isSupported) {
    return (
      <div
        style={{
          backgroundColor: "#fff3cd",
          border: "2px solid #ffc107",
          borderRadius: "12px",
          padding: "20px",
          marginTop: "20px",
          textAlign: "center",
        }}
      >
        <h4 style={{ color: "#856404", marginBottom: "8px" }}>
          ⚠️ Reconocimiento de voz no disponible
        </h4>
        <p style={{ color: "#856404", margin: 0 }}>
          Tu navegador no soporta esta funcionalidad
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="voice-chat-container">
        <div className="character-title" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: '12px' }}>
          <span>{generatedCharacter}</span>
          {generatedCharacter && (
            <div style={{ display: 'flex', gap: '8px' }}>
              <button
                onClick={handleShowCharacterInfo}
                style={{
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '6px 12px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  fontWeight: '500'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#007bff'}
                title="Ver información del personaje"
              >
                📖 Info
              </button>
              <button
                onClick={handleShowMovistarInfo}
                style={{
                  backgroundColor: '#e91e63',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '6px 12px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  fontWeight: '500'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#c2185b'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#e91e63'}
                title="Ver contenido en Movistar+"
              >
                🎬 Movistar+
              </button>
            </div>
          )}
        </div>

        {/* Progreso del juego */}
        {gameProgress && (
          <GameProgress
            gameProgress={gameProgress}
            onShowHints={handleShowHints}
          />
        )}

        {/* Conversation State Indicators */}
        <ConversationStateIndicator conversationState={conversationState} />

        {/* Animated Voice Button */}
        <div className="voice-button-container">
          <AnimatedVoiceButton
            state={conversationState}
            isActive={isActive}
            onClick={handleButtonClick}
          />
        </div>

        {/* Error Message */}
        {error && (
          <div className="error-message">
            ❌ {error}
          </div>
        )}

        {/* Chat Area */}
        <ChatArea
          messages={messages}
          isActive={isActive}
          isGameStarted={isGameStarted}
          initialMessage={initialMessage}
        />

        {/* Status Indicator */}
        <div className="status-indicator">
          💡 Habla cuando veas el micrófono pulsando • El sistema gestiona todo automáticamente
        </div>

        {/* Debug Information */}
        <div className="debug-info">
          Debug: isActive={isActive.toString()}, state={conversationState}, attempted={autoStartAttempted.current.toString()}
        </div>
      </div>

      {/* Hints Popup */}
      <HintsPopup
        hints={gameProgress?.hints || []}
        isOpen={showHintsPopup}
        onClose={handleCloseHints}
      />

      {/* Character Info Modal */}
      <CharacterInfo
        characterName={generatedCharacter || ''}
        isVisible={showCharacterInfo}
        onClose={handleCloseCharacterInfo}
      />

      {/* Movistar Info Modal */}
      <MovistarInfo
        characterName={generatedCharacter || ''}
        isVisible={showMovistarInfo}
        onClose={handleCloseMovistarInfo}
      />
    </>
  );
};
